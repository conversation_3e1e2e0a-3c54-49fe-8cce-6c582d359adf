import { notFound } from "next/navigation";
import { fetchData } from "@/client";
import BarDetail from "@/elements/bar-detail-page";
import {
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
} from "@/generated/graphql";
import { Metadata } from "next";
import NotFound from "@/app/not-found";

export const revalidate = 3600;

export async function generateStaticParams() {
  const res = await fetchData<BarsQuery, BarsQueryVariables>(BarsDocument)();

  const bars = res?.bars?.docs ?? [];

  // Only return the actual route parameters
  return bars.map((bar) => ({
    bar: bar.slug,
  }));
}

export const generateMetadata = async (props: {
  params: Promise<{ bar: string }>;
}): Promise<Metadata> => {
  const { bar: slugParam } = await props.params;

  // Fetch the bar data to get the metadata
  try {
    const res = await fetchData<BarsQuery, BarsQueryVariables>(BarsDocument, {
      barsInput: { slug: slugParam },
      paginationInput: { limit: 1 },
    })();

    const bar = res?.bars?.docs?.[0];

    if (!bar) {
      return {
        title: "Bar Not Found",
        description: "The requested bar could not be found.",
      };
    }

    return {
      title: `${bar.name} | Find Happy Hours, Photos & Opening Hours`,
      description: `Discover ${bar.name} check happy hour deals, see real photos, and get today's opening hours. Perfect for a fun night out.`,
      openGraph: {
        title: `${bar.name} | Find Happy Hours, Photos & Opening Hours`,
        description: `Discover ${bar.name} check happy hour deals, see real photos, and get today's opening hours. Perfect for a fun night out.`,
        url: `https://seeker.com/${bar.city.name.toLowerCase()}/bars/${slugParam}`,
        images: [
          {
            url: bar.coverImage ?? "",
            width: 1200,
            height: 630,
          },
        ],
      },
    };
  } catch (error) {
    return {
      title: `Bar Not Found ${error}`,
      description: "The requested bar could not be found.",
    };
  }
};

export default async function BarDetailPage({
  params,
}: {
  params: Promise<{ bar: string }>;
}) {
  const { bar: slugParam } = await params;

  try {
    const res = await fetchData<BarsQuery, BarsQueryVariables>(BarsDocument, {
      barsInput: { slug: slugParam },
      paginationInput: { limit: 1 },
    })();

    if (!res?.bars?.docs?.[0]) {
      return notFound();
    }

    const bar = res.bars.docs[0];
    const { bars: otherBars } = await fetchData<BarsQuery, BarsQueryVariables>(
      BarsDocument,
      { barsInput: { city: bar.city.id, featured: true } }
    )();

    return (
      <div className="bg-backgroundlight sm:bg-transparent">
        <BarDetail.BarDetailSection bar={bar} otherBars={otherBars} />
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
