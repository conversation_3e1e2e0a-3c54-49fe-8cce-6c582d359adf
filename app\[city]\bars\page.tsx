import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import BarCard from "@/components/cards/bar.card";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import BarsHomePage from "@/elements/bars-homepage";
import {
  BarCategoriesDocument,
  BarCategoriesQuery,
  BarCategoriesQueryVariables,
  BarCategoryByNameDocument,
  BarCategoryByNameQuery,
  BarCategoryByNameQueryVariables,
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
} from "@/generated/graphql";
import { formatCityName } from "@/lib/utils";
import { Metadata } from "next";
import Link from "next/link";

export const revalidate = 3600;

export async function generateStaticParams() {
  const res = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    {}
  )();

  const cities = res?.cities?.docs || [];

  return cities.map((city) => ({
    city: city.name,
    coverImage: city.coverImage,
  }));
}

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ city: string; coverImage: string }>;
}): Promise<Metadata> => {
  const { city: cityNameParam, coverImage: coverImageParam } = await params;

  return {
    title: `Find the Best Bars with Happy Hours in ${formatCityName(
      cityNameParam
    )} - Seeker Social`,
    description: `Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in town`,
    openGraph: {
      title: `Find the Best Bars with Happy Hours in ${formatCityName(
        cityNameParam
      )} - Seeker Social`,
      description: `Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in town`,
      url: `https://seeker.com/${cityNameParam}/bars`,
      images: [
        {
          url: coverImageParam,
          width: 1200,
          height: 630,
          alt: `Bars in ${cityNameParam}`,
        },
      ],
    },
  };
};

export default async function Bars({
  params,
  searchParams,
}: {
  params: Promise<{ city: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { category, page } = await searchParams;
  const currentPage = Number(page) || 1;

  const { city: cityParam } = await params;
  try {
    const activeCategory = category ? category : undefined;

    // Fetch cities with error handling
    const citiesResponse = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      {
        citiesInput: {
          name: cityParam.charAt(0).toUpperCase() + cityParam.slice(1),
        },
      }
    )();

    if (!citiesResponse?.cities?.docs?.[0]) {
      return <NotFound error={new Error("City not found")} />;
    }

    const city = citiesResponse.cities.docs[0];

    // Fetch bar categories with error handling
    const barCategoriesResponse = await fetchData<
      BarCategoriesQuery,
      BarCategoriesQueryVariables
    >(BarCategoriesDocument)();

    if (!barCategoriesResponse?.barCategories) {
      return <NotFound error={new Error("Bar categories not found")} />;
    }

    const { barCategories } = barCategoriesResponse;

    let categoryRes: BarCategoryByNameQuery | undefined;

    if (category?.length) {
      const res = await fetchData<
        BarCategoryByNameQuery,
        BarCategoryByNameQueryVariables
      >(BarCategoryByNameDocument, { name: category })();
      categoryRes = res;
    }

    // Fetch bars with proper error handling
    const barsResponse = await fetchData<BarsQuery, BarsQueryVariables>(
      BarsDocument,
      {
        barsInput: {
          city: city._id,
          categories: categoryRes?.barCategory?.id
            ? [categoryRes.barCategory.id]
            : undefined,
        },
        paginationInput: { limit: 8, page: currentPage },
      }
    )();

    // Check if bars response is valid
    if (!barsResponse?.bars) {
      return <NotFound error={new Error("Bars data not found")} />;
    }

    const { bars: barsWithFilter } = barsResponse;

    if (barsWithFilter.docs === undefined) {
      return <NotFound error={new Error("Bars not found")} />;
    }

    return (
      <>
        <BarsHomePage.HeroSection
          category={activeCategory}
          description={categoryRes?.barCategory?.description}
        />
        {category ? (
          <section id="categoryList" className="container">
            <div className="flex flex-row flex-wrap items-center justify-center gap-5 my-[65px] lg:my-[76px] ">
              {barsWithFilter?.docs?.map((bar) => (
                <Link
                  href={`bars/${bar.slug}`}
                  key={bar.id}
                  className="w-[298px]"
                >
                  <BarCard
                    cardType="bar"
                    data={bar}
                    wrapperClassName="w-[298px]"
                  />
                </Link>
              ))}
              <div className="w-full mt-2 lg:mt-6">
                <Pagination>
                  <PaginationContent>
                    {/* Previous Page */}
                    {barsWithFilter.hasPrevPage && (
                      <PaginationItem>
                        <PaginationPrevious
                          href={`?category=${category}&page=${barsWithFilter.prevPage}`}
                        />
                      </PaginationItem>
                    )}

                    {/* First Page */}
                    <PaginationItem>
                      <PaginationLink
                        href={`?category=${category}&page=1`}
                        isActive={currentPage === 1}
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>

                    {/* Second Page */}
                    {barsWithFilter.totalPages > 1 && (
                      <PaginationItem>
                        <PaginationLink
                          href={`?category=${category}&page=2`}
                          isActive={currentPage === 2}
                        >
                          2
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Ellipsis if currentPage is far from beginning */}
                    {currentPage > 4 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}

                    {/* Current Page (if not 1 or 2 or last 2) */}
                    {currentPage > 2 &&
                      currentPage < barsWithFilter.totalPages - 1 && (
                        <PaginationItem>
                          <PaginationLink
                            href={`?category=${category}&page=${currentPage}`}
                            isActive
                          >
                            {currentPage}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                    {/* Ellipsis before last 2 pages */}
                    {currentPage < barsWithFilter.totalPages - 3 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}

                    {/* Second Last Page */}
                    {barsWithFilter.totalPages > 3 && (
                      <PaginationItem>
                        <PaginationLink
                          href={`?category=${category}&page=${
                            barsWithFilter.totalPages - 1
                          }`}
                          isActive={
                            currentPage === barsWithFilter.totalPages - 1
                          }
                        >
                          {barsWithFilter.totalPages - 1}
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Last Page */}
                    {barsWithFilter.totalPages > 2 && (
                      <PaginationItem>
                        <PaginationLink
                          href={`?category=${category}&page=${barsWithFilter.totalPages}`}
                          isActive={currentPage === barsWithFilter.totalPages}
                        >
                          {barsWithFilter.totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Next Page */}
                    {barsWithFilter.hasNextPage && (
                      <PaginationItem>
                        <PaginationNext
                          href={`?category=${category}&page=${barsWithFilter.nextPage}`}
                        />
                      </PaginationItem>
                    )}
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
            <div></div>
            <div className="flex flex-col gap-4 lg:gap-[28px] container">
              <div className="font-bold text-2xl leading-5 text-secondary">
                Explore Other Bars
              </div>
              <div className="flex flex-row items-center flex-wrap gap-2 sm:gap- mb-[72px]">
                {barCategories?.docs
                  ?.filter((category) => category.name !== activeCategory)
                  .map((category) => (
                    <Link
                      key={category.id}
                      href={{ query: { category: category.name } }}
                      className="px-3 py-1.5 text-sm rounded-none sm:px-[28px] lg:text-xl font-bold leading-5 bg-primary text-white hover:text-background/80 transition-all duration-100 ease-in-out whitespace-nowrap"
                    >
                      {category.name}
                    </Link>
                  ))}
              </div>
            </div>
          </section>
        ) : (
          <>
            <BarsHomePage.BestBarsSection
              bars={barsWithFilter}
              city={city}
              categories={barCategories?.docs}
            />
            <BarsHomePage.BarsSection cityId={city.id} />
            <BarsHomePage.CallToDownloadSection />
          </>
        )}
      </>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
