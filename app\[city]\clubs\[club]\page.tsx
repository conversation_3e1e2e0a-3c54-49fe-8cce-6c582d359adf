import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import ClubDetail from "@/elements/club-detail-page";
import {
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import { Metadata } from "next";
import { notFound } from "next/navigation";

export const revalidate = 3600;

export async function generateStaticParams() {
  const res = await fetchData<ClubsQuery, ClubsQueryVariables>(ClubsDocument)();

  const clubs = res?.clubs?.docs || [];

  return clubs.map((club) => ({
    club: club.slug,
  }));
}

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{
    club: string;
  }>;
}): Promise<Metadata> => {
  const { club: slugParam } = await params;
  try {
    const res = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: { slug: slugParam },
        paginationInput: { limit: 1 },
      }
    )();

    const club = res?.clubs?.docs?.[0];
    if (!club) {
      return {
        title: "Club Not Found",
        description: "The requested club could not be found.",
      };
    }

    return {
      title: ` ${club.name}| Entry Prices, Opening Hours & Photos
`,
      description: `Find everything you need about ${club.name},  entry prices, guest list, opening hours, dress code, photos, and insider tips for your night out in City Name.`,
      openGraph: {
        title: ` ${club.name}| Entry Prices, Opening Hours & Photos
`,
        description: `Find everything you need about ${club.name},  entry prices, guest list, opening hours, dress code, photos, and insider tips for your night out in City Name.`,
        url: `https://seeker.com/clubs/${slugParam}`,
        images: [
          {
            url: club.coverImage ?? "",
            width: 1200,
            height: 630,
          },
        ],
      },
    };
  } catch (error) {
    return {
      title: `Club Not Found ${error}`,
      description: "The requested club could not be found.",
    };
  }
};

export default async function ClubDetailPage({
  params,
}: {
  params: Promise<{ club: string }>;
}) {
  const { club: slugParam } = await params;

  try {
    const { clubs: club } = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: { slug: slugParam },
        paginationInput: { limit: 1 },
      }
    )();

    const { clubs: otherClubs } = await fetchData<
      ClubsQuery,
      ClubsQueryVariables
    >(ClubsDocument, {
      paginationInput: { limit: 10 },
    })();

    if (!club?.docs?.[0]) {
      return notFound();
    }

    const clubDoc = club.docs[0];

    const faqDetails = {
      clubName: clubDoc.name,
      address: clubDoc.address?.address,
      closestMetroStation: clubDoc.address?.metroStation,
      openingHour: clubDoc.businessHours?.schedule?.[0]?.timings?.[0],
      closingHour: clubDoc.businessHours?.schedule?.[1]?.timings?.[1],
      currency: clubDoc.menu?.currency,
    };

    return (
      <div className="bg-backgroundlight sm:bg-transparent">
        <ClubDetail.ClubDetailSection club={club.docs[0]} />
        <ClubDetail.CallToDownloadSection />
        <ClubDetail.FAQSection {...faqDetails} />
        <ClubDetail.ExploreOtherClubsSection clubs={otherClubs.docs} />
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
