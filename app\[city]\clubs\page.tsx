import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import BarCard from "@/components/cards/bar.card";
import ClubsHomePage from "@/elements/clubs-homepage";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubCategoriesDocument,
  ClubCategoriesQuery,
  ClubCategoriesQueryVariables,
  ClubCategoryByNameDocument,
  ClubCategoryByNameQuery,
  ClubCategoryByNameQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import { Metadata } from "next";
import Link from "next/link";
import React from "react";
import { formatCityName } from "@/lib/utils";

export const revalidate = 3600;

export async function generateStaticParams() {
  const res = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    {}
  )();

  const cities = res?.cities?.docs || [];

  return cities.map((city) => ({
    city: city.name,
    coverImage: city.coverImage,
  }));
}

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ city: string; coverImage: string }>;
}): Promise<Metadata> => {
  const { city: cityNameParam, coverImage: coverImageParam } = await params;

  return {
    title: `Find the Best Clubs in ${formatCityName(
      cityNameParam
    )} | Techno, Hip-Hop, Rap, Latin & More
`,
    description: `Explore top nightclubs in ${formatCityName(
      cityNameParam
    )} by music style, from techno and rap to hip-hop and Latin parties. Discover opening hours, entry fees, photos, and plan your perfect night out.`,
    openGraph: {
      title: `Find the Best Clubs in ${formatCityName(
        cityNameParam
      )} | Techno, Hip-Hop, Rap, Latin & More
`,
      description: `Explore top nightclubs in ${formatCityName(
        cityNameParam
      )} by music style, from techno and rap to hip-hop and Latin parties. Discover opening hours, entry fees, photos, and plan your perfect night out.`,
      url: `https://seeker.com/${cityNameParam}/clubs`,
      images: [
        {
          url: coverImageParam,
          width: 1200,
          height: 630,
          alt: `Best clubs in ${cityNameParam}`,
        },
      ],
    },
  };
};

export default async function Clubs({
  params,
  searchParams,
}: {
  params: Promise<{ city: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { category, page } = await searchParams;
  const currentPage = Number(page) || 1;
  const { city: cityParam } = await params;
  try {
    const activeCategory = category ? category : undefined;

    const citiesResponse = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      {
        citiesInput: {
          name: cityParam.charAt(0).toUpperCase() + cityParam.slice(1),
        },
      }
    )();

    if (!citiesResponse?.cities?.docs?.[0]) {
      return <NotFound error={new Error("City not found")} />;
    }

    const city = citiesResponse.cities.docs[0];

    const clubCategoriesResponse = await fetchData<
      ClubCategoriesQuery,
      ClubCategoriesQueryVariables
    >(ClubCategoriesDocument)();

    const { clubCategories } = clubCategoriesResponse;

    let categoryRes: ClubCategoryByNameQuery | undefined;

    if (category?.length) {
      const res = await fetchData<
        ClubCategoryByNameQuery,
        ClubCategoryByNameQueryVariables
      >(ClubCategoryByNameDocument, { name: category })();
      categoryRes = res;
    }

    const clubsResponse = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: {
          city: city._id,
          categories: categoryRes?.clubCategory?.id
            ? [categoryRes.clubCategory.id]
            : undefined,
        },
        paginationInput: { limit: 8, page: currentPage },
      }
    )();

    if (!clubsResponse?.clubs) {
      return <NotFound error={new Error("Clubs not found")} />;
    }

    const { clubs: clubsWithFilter } = clubsResponse;

    if (clubsWithFilter.docs === undefined) {
      return <NotFound error={new Error("Clubs not found")} />;
    }

    return (
      <>
        <ClubsHomePage.HeroSection
          category={activeCategory}
          description={categoryRes?.clubCategory?.description}
        />
        {category ? (
          <section id="categoryList">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-9 container my-[65px] lg:my-[76px]">
              {clubsWithFilter?.docs?.map((club) => (
                <Link href={`clubs/${club.slug}`} key={club.id}>
                  <BarCard
                    cardType="club"
                    data={club}
                    wrapperClassName="w-[298px]"
                  />
                </Link>
              ))}
              <div className="col-span-full">
                <Pagination>
                  <PaginationContent>
                    {/* Previous Page */}
                    {clubsWithFilter.hasPrevPage && (
                      <PaginationItem>
                        <PaginationPrevious
                          href={`?category=${category}&page=${clubsWithFilter.prevPage}`}
                        />
                      </PaginationItem>
                    )}

                    {/* First Page */}
                    <PaginationItem>
                      <PaginationLink
                        href={`?category=${category}&page=1`}
                        isActive={currentPage === 1}
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>

                    {/* Second Page */}
                    {clubsWithFilter.totalPages > 1 && (
                      <PaginationItem>
                        <PaginationLink
                          href={`?category=${category}&page=2`}
                          isActive={currentPage === 2}
                        >
                          2
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Ellipsis if currentPage is far from beginning */}
                    {currentPage > 4 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}

                    {/* Current Page (if not 1 or 2 or last 2) */}
                    {currentPage > 2 &&
                      currentPage < clubsWithFilter.totalPages - 1 && (
                        <PaginationItem>
                          <PaginationLink
                            href={`?category=${category}&page=${currentPage}`}
                            isActive
                          >
                            {currentPage}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                    {/* Ellipsis before last 2 pages */}
                    {currentPage < clubsWithFilter.totalPages - 3 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}

                    {/* Second Last Page */}
                    {clubsWithFilter.totalPages > 3 && (
                      <PaginationItem>
                        <PaginationLink
                          href={`?category=${category}&page=${
                            clubsWithFilter.totalPages - 1
                          }`}
                          isActive={
                            currentPage === clubsWithFilter.totalPages - 1
                          }
                        >
                          {clubsWithFilter.totalPages - 1}
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Last Page */}
                    {clubsWithFilter.totalPages > 2 && (
                      <PaginationItem>
                        <PaginationLink
                          href={`?category=${category}&page=${clubsWithFilter.totalPages}`}
                          isActive={currentPage === clubsWithFilter.totalPages}
                        >
                          {clubsWithFilter.totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Next Page */}
                    {clubsWithFilter.hasNextPage && (
                      <PaginationItem>
                        <PaginationNext
                          href={`?category=${category}&page=${clubsWithFilter.nextPage}`}
                        />
                      </PaginationItem>
                    )}
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
            <div className="flex flex-col gap-[50px] lg:gap-[28px] container">
              <div className="font-bold text-2xl leading-5 text-secondary">
                Explore Other Clubs
              </div>
              <div className="flex flex-row items-center flex-wrap gap-2 sm:gap- mb-[72px]">
                {clubCategories?.docs
                  ?.filter((category) => category.name !== activeCategory)
                  .map((category) => (
                    <Link
                      key={category.id}
                      href={{ query: { category: category.name } }}
                      className="px-3 py-1.5 lg:py-2 text-sm sm:px-[28px] rounded-[2px] lg:text-xl font-bold leading-5 bg-primary text-white hover:text-secondary transition-all duration-400 ease-in-out whitespace-nowrap"
                    >
                      {category.name}
                    </Link>
                  ))}
              </div>
            </div>
          </section>
        ) : (
          <>
            <ClubsHomePage.BestClubsSection
              categories={clubCategories?.docs}
              clubs={clubsWithFilter}
              city={city}
            />
            <ClubsHomePage.ClubsSection cityId={city.id} />
            <ClubsHomePage.CallToDownloadSection />
          </>
        )}
      </>
    );
  } catch (error) {
    <NotFound error={error as Error} />;
  }
}
