import { fetchData } from "@/client";
import LandingPage from "@/elements/landing-page";
import {
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";

import { Metadata } from "next";
import NotFound from "../not-found";
import { formatCityName } from "@/lib/utils";

export const revalidate = 3600;

export const generateStaticParams = async () => {
  const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument
  )();

  const city = citiesRes?.cities?.docs?.[0];
  if (!city) {
    throw new Error("City data not found");
  }

  return [
    {
      cityId: city.name,
    },
  ];
};

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ city: string }>; // Changed to Promise
}): Promise<Metadata> => {
  const { city: cityNameParam } = await params; // Await the params

  const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    {
      citiesInput: {
        name: cityNameParam.charAt(0).toUpperCase() + cityNameParam.slice(1),
      },
    }
  )();
  const city = citiesRes?.cities?.docs?.[0];
  const coverImage = city?.coverImage;

  return {
    title: `Find the Best Bars with Happy Hours in ${formatCityName(
      cityNameParam
    )} - Seeker Social`,
    description: `Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in ${cityNameParam}`,
    openGraph: {
      title: `Find the Best Bars with Happy Hours in ${formatCityName(
        cityNameParam
      )} - Seeker Social`,
      description: `Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in ${cityNameParam}`,
      url: `https://seeker.com/${cityNameParam}`,
      images: [
        {
          url: coverImage ?? "",
          width: 1200,
          height: 630,
          alt: `Bars in ${cityNameParam}`,
        },
      ],
    },
  };
};

export default async function CityPage({
  params,
}: {
  params: Promise<{ city: string }>;
}) {
  try {
    const cityName = (await params).city;

    const cityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      {
        citiesInput: {
          name: cityName.charAt(0).toUpperCase() + cityName.slice(1),
        },
      }
    )();

    const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument
    )();

    if (!citiesRes?.cities?.docs?.[0]) {
      throw new Error("Cities data not found");
    }

    if (!cityRes?.cities?.docs?.[0]) {
      throw new Error("City data not found");
    }

    const city = cityRes.cities.docs[0];

    const barsRes = await fetchData<BarsQuery, BarsQueryVariables>(
      BarsDocument,
      {
        barsInput: { city: city._id, featured: true },
        paginationInput: { limit: 10 },
      }
    )();

    if (!barsRes?.bars) {
      throw new Error("Bars data not found");
    }

    const clubsRes = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: { city: city._id, featured: true },
        paginationInput: { limit: 10 },
      }
    )();

    if (!clubsRes?.clubs) {
      throw new Error("Clubs data not found");
    }

    return (
      <div>
        {city?.coverImage ? <LandingPage.HeroSection {...city} /> : null}
        <LandingPage.PopularSections
          cities={citiesRes.cities.docs}
          bars={barsRes.bars.docs}
          clubs={clubsRes.clubs.docs}
        />
        <LandingPage.CallToDownloadSection />
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
