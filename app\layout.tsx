import Navbar from "@/components/common/Navbar";
import RootProvider from "@/providers/root.provider";
import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { fetchData } from "@/client";
import {
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
} from "@/generated/graphql";
import Footer from "@/components/common/Footer";
import NotFound from "./not-found";

const montserrat_alternates = localFont({
  src: [
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-Light.ttf",
      weight: "300",
    },
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-Regular.ttf",
      weight: "400",
    },
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-Medium.ttf",
      weight: "500",
    },
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-SemiBold.ttf",
      weight: "600",
    },

    {
      path: "./fonts/montserrat-alter/MontserratAlternates-Bold.ttf",
      weight: "700",
    },
    {
      path: "./fonts/montserrat-alter/MontserratAlternates-ExtraBold.ttf",
      weight: "800",
    },
  ],
  variable: "--font-montserrat-alternates",
});

const montserrat = localFont({
  src: [
    {
      path: "./fonts/montserrat/Montserrat-Light.ttf",
      weight: "300",
    },
    {
      path: "./fonts/montserrat/Montserrat-Regular.ttf",
      weight: "400",
    },
    {
      path: "./fonts/montserrat/Montserrat-Medium.ttf",
      weight: "500",
    },
    {
      path: "./fonts/montserrat/Montserrat-SemiBold.ttf",
      weight: "600",
    },

    {
      path: "./fonts/montserrat/Montserrat-Bold.ttf",
      weight: "700",
    },
    {
      path: "./fonts/montserrat/Montserrat-ExtraBold.ttf",
      weight: "800",
    },
  ],
  variable: "--font-montserrat",
});

export const metadata: Metadata = {
  title: "Seeker",
  description:
    "Seeker is a platform that connects users with bars and clubs, offering a seamless way to explore venues across cities.",
  icons: {
    icon: "/svg/tempLogo.svg", // Replace with actual icon after design is ready
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  try {
    const citiesQuery = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      {
        paginationInput: { limit: 5 },
      }
    )();
    if (!citiesQuery.cities.docs) {
      throw new Error("Cities not found");
    }

    return (
      <html lang="en" className="scroll-smooth">
        <body
          className={`${montserrat_alternates.variable} ${montserrat.variable}  antialiased font-montserrat-alternates`}
        >
          <RootProvider>
            <Navbar {...citiesQuery.cities} />
            {children}
            <Footer {...citiesQuery.cities} />
          </RootProvider>
        </body>
      </html>
    );
  } catch (error) {
    return (
      <html lang="en" className="scroll-smooth">
        <body className="">
          <NotFound error={error as Error} />
        </body>
      </html>
    );
  }
}
