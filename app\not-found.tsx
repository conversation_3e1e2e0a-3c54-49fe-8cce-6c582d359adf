import Link from "next/link";

export default function NotFound({ error }: { error: <PERSON>rror }) {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white text-secondary px-4">
      <h1 className="text-6xl font-bold mb-4">
        {error instanceof Error
          ? error.message
          : "An unexpected error occurred"}
      </h1>
      <p className="text-xl mb-6">
        Oops! The page you&apos;re looking for doesn&apos;t exist.
      </p>
      <Link
        href="/"
        className="text-blue-600 hover:underline text-base font-medium"
      >
        Go back home
      </Link>
    </div>
  );
}
