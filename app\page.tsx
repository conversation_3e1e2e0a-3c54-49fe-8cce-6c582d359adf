import { fetchData } from "@/client";
import LandingPage from "@/elements/landing-page";
import {
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import NotFound from "./not-found";

export const revalidate = 3600;

export const metadata = {
  title: `Find the Best Bars with Happy Hours in Paris - Seeker Social`,
  description:
    "Discover all kinds of bars with great happy hour deals near you, perfect for students, afterwork drinks, or nights out with friends. Cheap cocktails, beers, and the best terraces in town",
};

export default async function LandingPageScreen() {
  try {
    // Fetch city data with error handling
    const cityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument,
      { citiesInput: { name: "Paris" } }
    )();
    const citiesRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
      CitiesDocument
    )();

    if (!citiesRes?.cities?.docs?.[0]) {
      throw new Error("Cities data not found");
    }

    if (!cityRes?.cities?.docs?.[0]) {
      throw new Error("City data not found");
    }

    const city = cityRes.cities.docs[0];

    // Fetch bars with error handling
    const barsRes = await fetchData<BarsQuery, BarsQueryVariables>(
      BarsDocument,
      {
        barsInput: { city: city._id, featured: true },
        paginationInput: { limit: 10 },
      }
    )();

    if (!barsRes?.bars) {
      throw new Error("Bars data not found");
    }

    const bars = barsRes.bars;

    // Fetch clubs with error handling
    const clubsRes = await fetchData<ClubsQuery, ClubsQueryVariables>(
      ClubsDocument,
      {
        clubsInput: { city: city._id, featured: true },
        paginationInput: { limit: 10 },
      }
    )();

    if (!clubsRes?.clubs) {
      throw new Error("Clubs data not found");
    }

    const clubs = clubsRes.clubs;
    const cities = citiesRes.cities.docs;

    return (
      <div>
        {city?.coverImage ? <LandingPage.HeroSection {...city} /> : null}
        <LandingPage.PopularSections
          cities={cities}
          bars={bars?.docs}
          clubs={clubs?.docs}
        />
        <LandingPage.CallToDownloadSection />
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
