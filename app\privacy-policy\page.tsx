import Link from "next/link";
import React from "react";

export default function PrivacyPolicy() {
  return (
    <>
      <div className="w-full py-8 lg:py-[75px] contact-banner-gradient flex items-center justify-center">
        <h1 className="text-3xl lg:text-[40px] text-white font-bold container text-center">
          Privacy Policy
        </h1>
      </div>
      <div className="container my-4 lg:py-[80px] lg:px-8">
        <div className="text-secondary font-medium text-base lg:text-xl font-montserrat mb-4 lg:mb-6">
          This Privacy Policy describes how Seeker Social (&quot;we,&quot;
          &quot;us,&quot; or &quot;our&quot;) collects, uses, and protects your
          information when you use our mobile application (the &quot;App&quot;).
          We are committed to protecting your privacy and complying with the
          General Data Protection Regulation (GDPR).
        </div>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          1. Data Controller
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          Seeker Social is the data controller for the information you provide
          through the App. You can contact <NAME_EMAIL>.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          2. Information We Collect
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-1">
          We collect the following types of information:
        </p>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mr-2 mt-1.5 lg:mt-2.5" />
          <div className="text-text text-sm lg:text-lg font-montserrat font-bold">
            Information You Provide Voluntarily:
          </div>
        </div>
        <div className="flex flex-row items-start mb-1 ml-8 lg:ml-16">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              Account Creation:
            </span>{" "}
            When you create an account, you agree to provide accurate and
            complete information, such as your email address.
          </p>
        </div>
        <div className="flex flex-row items-start mb-1 ml-8 lg:ml-16">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5  mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              City Selection:
            </span>{" "}
            To provide relevant listings, we ask you to select a city. This is
            not tied to your precise real-time location.
          </p>
        </div>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mr-2 mt-1.5 lg:mt-2.5" />
          <div className="text-text text-sm lg:text-lg font-montserrat font-bold">
            Information Collected Automatically with Your Consent:
          </div>
        </div>
        <div className="flex flex-row items-start mb-1 ml-8 lg:ml-16">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              Location Data:
            </span>{" "}
            With your explicit permission, the App may use your device&apos;s
            location to suggest nearby venues. You can enable or disable
            location services at any time through your device settings. Use of
            location data is not mandatory to use the App.
          </p>
        </div>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6 ml-8 lg:ml-16">
          We do not collect other personal information, such as device
          information.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          3. How We Use Your Information
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-1">
          We use the information we collect for the following purposes:
        </p>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              To Provide and Personalize Our Services:
            </span>{" "}
            To show you relevant bars and clubs based on your selected city or
            location.
          </p>
        </div>
        <div className="flex flex-row items-start mb-4 lg:mb-6 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              For Marketing and Communications:
            </span>{" "}
            To send you updates, promotional materials, and other information
            related to Seeker Social. You can opt out of marketing
            communications at any time.
          </p>
        </div>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          4. Legal Basis for Processing
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-1">
          Our legal basis for processing your personal data under GDPR is:
        </p>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              Consent:
            </span>{" "}
            We rely on your consent to process information for marketing
            purposes and to access your device&apos;s location data. You can
            your consent at any time.
          </p>
        </div>
        <div className="flex flex-row items-start mb-4 lg:mb-6 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              Legitimate Interest:
            </span>{" "}
            We process your account information and city selection to provide
            the core services of the App.
          </p>
        </div>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          5. Data Sharing and Disclosure
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          We do not sell, trade, or otherwise share your personal data with any
          third parties, including the venues listed on our app.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          6. Data Retention
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          We will retain your personal information only for as long as is
          necessary for the purposes set out in this Privacy Policy, such as
          maintaining your account.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          7. Your Rights Under GDPR
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-1">
          As a user in the EU, you have the following rights regarding your
          personal data:
        </p>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              The right to access –
            </span>{" "}
            You can request copies of your personal data.
          </p>
        </div>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              The right to rectification –
            </span>{" "}
            You can request that we correct any information you believe is
            inaccurate.
          </p>
        </div>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              The right to erasure –
            </span>{" "}
            You can request that we erase your personal data, under certain
            conditions.
          </p>
        </div>
        <div className="flex flex-row items-start mb-1 ml-2 lg:ml-4">
          <div className="min-w-2 min-h-2 rounded-full bg-text mt-1.5 lg:mt-2.5 mr-2" />
          <p className="text-text text-sm lg:text-lg font-montserrat">
            <span className="text-text text-sm lg:text-lg font-montserrat font-bold">
              The right to restrict processing –
            </span>{" "}
            You have the right to object to our processing of your personal
            data.
          </p>
        </div>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          To exercise any of these rights, please contact us at{" "}
          <Link
            className="font-semibold text-blue-400 hover:underline hover:underline-offset-2"
            href={"mailto:<EMAIL>"}
          >
            <EMAIL>.
          </Link>
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          8. Data Security
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          We take reasonable measures to protect the information you provide to
          us from loss, theft, misuse, and unauthorized access.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          9. Changes to This Privacy Policy
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          We may update this Privacy Policy from time to time. We will notify
          you of any changes by posting the new policy within the App.
        </p>
        <h3 className="text-text text-base lg:text-xl font-montserrat font-bold mb-1">
          10. Contact Us
        </h3>
        <p className="text-text text-sm lg:text-lg font-montserrat mb-4 lg:mb-6">
          If you have any questions about this Privacy Policy, please contact us
          at{" "}
          <Link
            className="font-semibold text-blue-400 hover:underline hover:underline-offset-2"
            href={"mailto:<EMAIL>"}
          >
            <EMAIL>.
          </Link>
        </p>
      </div>
    </>
  );
}
