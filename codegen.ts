import { CodegenConfig } from "@graphql-codegen/cli";
import { NEXT_GRAPHQL_URL } from "./env";

const config: CodegenConfig = {
  schema: NEXT_GRAPHQL_URL,
  // schema: "https://api.seeker.social/graphql",
  documents: ["./graphql/**/*.{ts,gql}"],
  ignoreNoDocuments: true, // for better experience with the watcher
  generates: {
    "./generated/graphql.ts": {
      plugins: [
        "typescript",
        "typescript-operations",
        "typescript-react-query",
      ],
      config: {
        isReactHook: false,
        fetcher: "@/client#fetchData",
        errorType: "Error",
        exposeDocument: true,
        exposeQueryKeys: true,
        legacyMode: false,
        reactQueryVersion: 5,
        scalars: {
          DateTime: "Date",
          JSON: "any",
        },
      },
    },
  },
};

export default config;
