import COLORS from "@/constants/colors";

import ICONS from "@/public/icons";
import Image from "next/image";

type CityCardProps = {
  totalBars: number;
  totalClubs: number;
  cityImage: string | null | undefined;
  cityName: string;
};

export default function CityCard({
  cityImage,
  cityName,
  totalBars,
  totalClubs,
}: CityCardProps) {
  return (
    <div className="w-[310px] min-w-[310px] bg-white rounded-[21px] flex flex-col justify-center p-[14px] shadow-lg shadow-neutral-300/20 border border-neutral-200/30">
      <div className="w-full overflow-hidden rounded-[21px]">
        <Image
          src={cityImage || "/images/placeholderEmpty.png"}
          width={279}
          height={173}
          alt="card"
          className="object-cover w-[279px] h-[173px] aspect-square md:object-cover transition-all duration-500"
        />
      </div>
      <div className="flex flex-row items-center gap-2.5 text-xl leading-[29px] text-black truncate max-w-[170px] mt-[23px] mb-[26px] pl-4">
        {cityName}
      </div>
      <div className="flex flex-row items-center gap-4 mb-2 pl-4">
        <div className="flex flex-row items-center gap-1.5">
          <div className="w-[39px] h-[38px] bg-transparent rounded-full border border-primary p-1 flex items-center justify-center">
            <ICONS.HappyHourIcon
              width={22}
              height={24}
              color={COLORS.LIGHT.PRIMARY}
            />
          </div>
          <div className="font-montserrat font-semibold italic text-primary text-2xl">
            {totalClubs}+
          </div>
        </div>
        <div className="flex flex-row items-center gap-1.5">
          <div className="w-[39px] h-[38px] bg-transparent rounded-full border border-primary p-1 flex items-center justify-center">
            <ICONS.BeerJugsIcon
              width={25}
              height={23}
              color={COLORS.LIGHT.PRIMARY}
            />
          </div>
          <div className="font-montserrat font-semibold italic text-primary text-2xl">
            {totalBars}+
          </div>
        </div>
      </div>
    </div>
  );
}
