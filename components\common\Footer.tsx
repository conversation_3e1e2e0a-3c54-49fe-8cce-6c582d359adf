"use client";

import React, { useState } from "react";
import AppButton from "./AppButton";
import Image from "next/image";
import ICONS from "@/public/icons";
import COLORS from "@/constants/colors";
import Link from "next/link";
import { CitiesQuery } from "@/generated/graphql";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type City = CitiesQuery["cities"];

const icons = [{ icon: ICONS.InstagramIcon }];

export default function Footer(cities: City) {
  const { docs } = cities;
  const [email, setEmail] = useState<string>("");
  const onSubmit = () => {
    console.log("EMAIL ADDRESS : ", email);
    setEmail("");
  };
  return (
    <>
      {/* MOBILE FOOTER */}
      <footer className="w-full pt-8 pb-20 bg-footerbg overflow-hidden relative lg:hidden">
        <div className="flex flex-col md:grid md:grid-cols-2 gap-5 mb-10 mx-4">
          {docs.map((city) => (
            <Link
              href={`/${city.name.toLowerCase()}/bars`}
              key={city.id}
              className="text-base font-montserrat-alternates font-medium text-secondary"
            >
              Best bars in {city.name}
            </Link>
          ))}
        </div>
        <div className="flex flex-col md:grid md:grid-cols-2 gap-6.5 border-b pb-9 border-muted mb-9 mx-4">
          {docs.map((city) => (
            <Link
              href={`/${city.name.toLowerCase()}/clubs`}
              key={city.id}
              className="text-base font-montserrat-alternates font-medium text-secondary"
            >
              Best clubs in {city.name}
            </Link>
          ))}
        </div>
        <div className="grid grid-cols-2 mx-4 gap-5 mb-6 pb-9 border-b border-muted">
          <Link
            className="text-base font-montserrat-alternates font-medium text-secondary"
            href={`/#explore`}
          >
            Bars
          </Link>
          <Link
            className="text-base font-montserrat-alternates font-medium text-secondary"
            href={"/#explore"}
          >
            Clubs
          </Link>
          <Link
            className="text-base font-montserrat-alternates font-medium text-secondary"
            href={"/contact"}
          >
            Contact us
          </Link>
          <Link
            className="text-base font-montserrat-alternates font-medium text-secondary"
            href={"/terms-and-conditions"}
          >
            Terms & Conditions
          </Link>
          <Link
            className="text-base font-montserrat-alternates font-medium text-secondary"
            href={"/privacy-policy"}
          >
            Privacy Policy
          </Link>
        </div>
        <div className="w-full px-4">
          <p className="font-montserrat-alternates font-semibold text-base leading-[26px] text-text mb-2">
            Get recent news and updates
          </p>
          <div className="bg-white rounded-full w-full flex flex-row items-center justify-between mb-4">
            <input
              className="outline-none bg-transparent text-base font-montserrat-alternates text-black pl-4 pr-2 py-4"
              placeholder="Email Address"
              onChange={(e) => setEmail(e.target.value)}
              value={email}
            />
            <AppButton
              className="py-4 px-5 font-bold font-montserrat"
              type="submit"
              onClick={onSubmit}
              variant="gradient2"
            >
              SUBMIT
            </AppButton>
          </div>
        </div>
        <div className="w-full px-4">
          <div className="w-full py-8 px-8 bg-white rounded-[20px] flex flex-col items-center justify-center gap-2.5">
            <Image
              src={"/logo.svg"}
              width={288}
              height={94}
              alt="footer-logo"
              className="w-[288px] h-[94px]"
            />
            <div className="flex flex-row items-center gap-4">
              <span className="text-2xl font-montserrat-alternates text-black font-medium">
                find your vibe
              </span>
              {icons.map((item, idx) => (
                <button
                  key={idx}
                  className="min-w-[27px] min-h-[27px] bg-gradient-to-br from-text to-secondary flex items-center justify-center rounded-full"
                >
                  <item.icon
                    width={15}
                    height={15}
                    color={COLORS.LIGHT.WHITE}
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
        <div className="flex flex-row items-center justify-between gap-8 mt-6 mx-4">
          <AppButton
            variant="secondary"
            className="rounded-xl px-2 py-2 flex items-center justify-center"
          >
            <Image
              src={"/svg/googleplay.svg"}
              alt="googleplay"
              width={140}
              height={35}
              className="ml-4 w-[140px] h-[35px]"
            />
          </AppButton>
          <AppButton variant="secondary" className="rounded-xl px-2 py-2">
            <Image
              src={"/svg/applestore.svg"}
              alt="googleplay"
              width={140}
              height={35}
              className="ml-4 w-[140px] h-[35px]"
            />
          </AppButton>
        </div>
        <div className="w-full py-3.5 bg-primary text-white font-montserrat-alternates font-medium text-center absolute bottom-0">
          © 2025 Seeker.social. All rights reserved.
        </div>
      </footer>

      {/* WEB FOOTER */}
      <footer className="hidden w-full pt-8 pb-[38px] bg-footerbg overflow-hidden relative lg:flex">
        <div className="flex flex-row items-center container pt-[76px] pb-[69px]">
          <div className="flex flex-col h-full w-[40%] px-4 xl:px-0">
            <div className="w-[367px] pt-8 pb-[38px] bg-white rounded-[20px] flex flex-col items-center justify-center gap-4">
              <Image
                src={"/logo.svg"}
                width={289}
                height={94}
                alt="footer-logo"
                className="w-[289px] h-[94px]"
                loading="eager"
                quality={100}
              />
              <div className="flex flex-row items-center w-full justify-between px-14">
                <span className="text-2xl text-black font-medium">
                  find your vibe
                </span>
                {icons.map((icon, idx) => (
                  <Link
                    href={"https://www.instagram.com/seeker.social"}
                    target="_blank"
                    className="min-w-[27px] min-h-[27px] bg-gradient-to-br from-text to-secondary flex items-center justify-center rounded-full"
                    key={idx}
                  >
                    <icon.icon
                      width={15}
                      height={15}
                      color={COLORS.LIGHT.WHITE}
                    />
                  </Link>
                ))}
              </div>
            </div>
            <div className="w-[367px] flex flex-row items-center justify-center gap-[31px] mt-[39px]">
              <Tooltip>
                <TooltipTrigger>
                  <Link
                    href={``}
                    className="rounded-xl px-2 py-[13px] bg-gradient-to-br from-text to-secondary font-semibold flex flex-1 items-center justify-center w-[168px]"
                  >
                    <Image
                      src={"/svg/googleplay.svg"}
                      alt="googleplay"
                      width={140}
                      height={35}
                      className="ml-4 w-[140px] h-[35px]"
                    />
                  </Link>
                </TooltipTrigger>
                <TooltipContent className="bg-transparent">
                  <p className="text-sm font-medium bg-muted-primary text-black  px-4 py-1.5 rounded-[8px]">
                    Coming Soon
                  </p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger>
                  <Link
                    href={``}
                    className="rounded-xl px-2 py-[13px] bg-gradient-to-br from-text to-secondary font-semibold flex flex-1 items-center justify-center w-[168px]"
                  >
                    <Image
                      src={"/svg/applestore.svg"}
                      alt="googleplay"
                      width={140}
                      height={35}
                      className="ml-4 w-[140px] h-[35px]"
                    />
                  </Link>
                </TooltipTrigger>
                <TooltipContent className="bg-transparent">
                  <p className="text-sm font-medium bg-muted-primary text-black  px-4 py-1.5 rounded-[8px]">
                    Coming Soon
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>
          <div className="flex flex-col">
            <div className="flex flex-row items-start justify-between ">
              <div className="flex flex-col gap-4 xl:gap-x-[90px] mb-9 mx-4">
                {docs.map((city) => (
                  <Link
                    href={`/${city.name.toLowerCase()}/bars`}
                    key={city.id}
                    className="text-base font-montserrat-alternates font-medium text-secondary"
                  >
                    Best bars in {city.name}
                  </Link>
                ))}
              </div>
              <div className="flex flex-col gap-4 xl:gap-x-[90px] mb-9 mx-4">
                {docs.map((city) => (
                  <Link
                    href={`/${city.name.toLowerCase()}/clubs`}
                    key={city.id}
                    className="text-base font-montserrat-alternates font-medium text-secondary"
                  >
                    Best clubs in {city.name}
                  </Link>
                ))}
              </div>
              <div className="grid grid-cols-1 gap-4 mb-9 xl:ml-[60px]">
                <Link
                  className="font-montserrat-alternates text-base font-medium text-secondary hover:underline hover:underline-offset-2"
                  href={`/#explore`}
                >
                  Bars
                </Link>
                <Link
                  className="font-montserrat-alternates text-base font-medium text-secondary hover:underline hover:underline-offset-2"
                  href={"/#explore"}
                >
                  Clubs
                </Link>
                <Link
                  className="font-montserrat-alternates text-base font-medium text-secondary hover:underline hover:underline-offset-2"
                  href={"/contact"}
                >
                  Contact us
                </Link>
                <Link
                  className="font-montserrat-alternates text-base font-medium text-secondary hover:underline hover:underline-offset-2"
                  href={"/terms-and-conditions"}
                >
                  Terms & Conditions
                </Link>
                <Link
                  className="font-montserrat-alternates text-base font-medium text-secondary hover:underline hover:underline-offset-2"
                  href={"/privacy-policy"}
                >
                  Privacy Policy
                </Link>
              </div>
            </div>
            <p className="font-montserrat-alternates font-semibold text-base leading-[26px] text-text mx-4">
              Get recent news and updates
            </p>
            <div className="bg-white rounded-full flex flex-row items-center justify-between mb-4 mx-4 lg:max-w-[430px] mt-2">
              <input
                className="outline-none bg-transparent text-base font-montserrat-alternates text-black pl-4 pr-2 py-4"
                placeholder="Email Address"
                onChange={(e) => setEmail(e.target.value)}
                value={email}
              />
              <AppButton
                className="py-4 px-5 font-bold font-montserrat"
                type="submit"
                onClick={onSubmit}
                variant="gradient2"
              >
                SUBMIT
              </AppButton>
            </div>
          </div>
        </div>
        <div className="w-full py-3.5 bg-primary text-white  font-medium text-center absolute bottom-0">
          © 2025 Seeker.social. All rights reserved.
        </div>
      </footer>
    </>
  );
}
