"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import {
  She<PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetHeader,
  SheetT<PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import COLORS from "@/constants/colors";
import { CitiesQuery } from "@/generated/graphql";
import { cn } from "@/lib/utils";
import ICONS from "@/public/icons";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";

type City = CitiesQuery["cities"];

function ListItem({
  title,
  children,
  href,
  onHover,
  ...props
}: React.ComponentPropsWithoutRef<"li"> & {
  href: string;
  onHover?: () => void;
}) {
  return (
    <li
      {...props}
      onMouseEnter={onHover}
      className="bg-transparent transition-colors rounded-sm px-2"
    >
      <NavigationMenuLink asChild>
        <Link href={href}>
          <div className="text-sm leading-none font-medium">{title}</div>
          <p className="text-muted-foreground line-clamp-2 text-sm leading-snug">
            {children}
          </p>
        </Link>
      </NavigationMenuLink>
    </li>
  );
}

export default function Navbar(cities: City) {
  const { docs } = cities;
  const [hoveredImage, setHoveredImage] = useState<string | null | undefined>(
    docs[0].image
  );
  const [open, setOpen] = useState(false);

  return (
    <>
      {/* WEB VIEW */}
      <nav className="w-full container py-4 bg-background sm:flex sm:items-center sm:justify-between hidden">
        <Link href={"/"}>
          <Image
            src="/logo.svg"
            width={130}
            height={40}
            alt="logo"
            className="w-[130px] h-[40px] lg:w-[175px] lg:h-[57px]"
          />
        </Link>
        <div className="flex flex-1 justify-end mt-2">
          <NavigationMenu viewport={false}>
            <NavigationMenuList className="sm:gap-0">
              {/* BARS LIST */}
              <NavigationMenuItem>
                <NavigationMenuTrigger>Bars</NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-2 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                    {/* Image Preview Section */}
                    <NavigationMenuLink asChild className="">
                      <li className="row-span-5 flex items-center justify-center w-[200px] h-[300px] rounded-md overflow-hidden">
                        <Image
                          src={hoveredImage ?? "/logo.svg"}
                          width={175}
                          height={120}
                          alt="City preview"
                          className="rounded-md object-cover transition-all duration-300 w-full h-full"
                          loading="eager"
                          quality={100}
                          priority={true}
                        />
                      </li>
                    </NavigationMenuLink>

                    {/* City Links */}
                    {docs?.map((city) => (
                      <ListItem
                        key={city.id}
                        href={`/${city.name.toLowerCase()}/bars`}
                        title={city.name}
                        onHover={() =>
                          setHoveredImage(city.image || "/logo.svg")
                        }
                      >
                        {city.heading}
                      </ListItem>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* CLUBS LIST */}
              <NavigationMenuItem>
                <NavigationMenuTrigger>Clubs</NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-2 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                    {/* Image Preview Section */}

                    <NavigationMenuLink asChild>
                      <li className="row-span-5 flex items-center justify-center w-[200px] h-[300px] rounded-md overflow-hidden">
                        <Image
                          src={hoveredImage ?? "/logo.svg"}
                          width={175}
                          height={120}
                          alt="City preview"
                          className="rounded-md object-cover transition-all duration-300 w-full h-full"
                          loading="eager"
                          quality={100}
                          priority={true}
                        />
                      </li>
                    </NavigationMenuLink>

                    {/* City Links */}
                    {docs?.map((city) => (
                      <ListItem
                        key={city.id}
                        href={`/${city.name.toLowerCase()}/clubs`}
                        title={city.name}
                        onHover={() =>
                          setHoveredImage(city.image || "/logo.svg")
                        }
                      >
                        {city.heading}
                      </ListItem>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* DISABLED EVENT BUTTON (ADD TOOLTIP AS COMING SOON) */}

              <Tooltip>
                <TooltipTrigger>
                  <NavigationMenuItem>
                    <NavigationMenuLink
                      asChild
                      className="text-sm text-text lg:text-base font-montserrat-alternates xl:mr-0 hover:bg-transparent focus:bg-transparent data-[state=open]:hover:bg-transparent data-[state=open]:focus:bg-transparent data-[state=open]:bg-transparent"
                    >
                      <Link href="/">Events</Link>
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm font-medium text-primary">
                    Coming Soon
                  </p>
                </TooltipContent>
              </Tooltip>

              {/* CONTACT US BUTTON */}
              <NavigationMenuItem>
                <NavigationMenuLink
                  asChild
                  className="text-sm text-text lg:text-base font-montserrat-alternates xl:mr-0 hover:bg-transparent focus:bg-transparent data-[state=open]:hover:bg-transparent data-[state=open]:focus:bg-transparent data-[state=open]:bg-transparent"
                >
                  <Link href="/contact">Contact Us</Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              {/* DOWNLOAD APP BUTTON */}
              <Tooltip>
                <TooltipTrigger>
                  <Link
                    href={""}
                    className="px-4 ml-4 lg:px-7 py-3.5 rounded-full bg-gradient-to-br from-muted-primary via-primary to-primary font-semibold text-white"
                  >
                    DOWNLOAD APP
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm font-medium text-primary">
                    Coming soon
                  </p>
                </TooltipContent>
              </Tooltip>
            </NavigationMenuList>
          </NavigationMenu>
        </div>
      </nav>
      {/* MOBILE VIEW */}
      <nav className="w-full py-5 px-4 relative bg-background flex flex-row items-center justify-between sm:hidden">
        <Link href={"/"}>
          <Image
            src="/logo.svg"
            width={110}
            height={36}
            alt="logo"
            className="w-[110px] h-[36px]"
          />
        </Link>
        <Sheet open={open} onOpenChange={setOpen} modal={false}>
          <SheetTrigger asChild>
            <button>
              <ICONS.NavbarIcon
                width={25}
                height={21}
                color={COLORS.LIGHT.BLACK}
              />
            </button>
          </SheetTrigger>
          <SheetContent className="bg-background gap-0 overflow-y-auto">
            <SheetHeader className="bg-transparent">
              <SheetTitle className="w-full flex items-center justify-center -mt-1 pb-4 border-b border-muted">
                <Image
                  src="/logo.svg"
                  width={90}
                  height={36}
                  alt="logo"
                  className="w-[90px] h-[36px]"
                />
              </SheetTitle>
            </SheetHeader>
            <Accordion type="single" className="px-4" collapsible>
              <AccordionItem value="bars">
                <AccordionTrigger>
                  <p className="text-xl font-semibold text-left w-full">Bars</p>
                </AccordionTrigger>
                <AccordionContent className="bg-transparent flex flex-col gap-2">
                  {docs?.map((city, idx) => (
                    <Link
                      href={`/${city.name}/bars`}
                      key={city.id}
                      className={cn(
                        "flex flex-col gap-1 py-2 border-b border-inactive",
                        docs.length - 1 === idx && "border-b-0"
                      )}
                      onClick={() => setOpen(false)}
                    >
                      <p className="font-semibold">{city.name}</p>
                      <p className="text-sm font-medium">{city.heading}</p>
                    </Link>
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
            <Accordion type="single" className="px-4" collapsible>
              <AccordionItem value="clubs">
                <AccordionTrigger>
                  <p className="text-xl font-semibold text-left w-full text-text">
                    Clubs
                  </p>
                </AccordionTrigger>
                <AccordionContent className="bg-transparent flex flex-col gap-2">
                  {docs?.map((city, idx) => (
                    <Link
                      href={`/${city.name}/clubs`}
                      key={city.id}
                      className={cn(
                        "flex flex-col gap-1 py-2 border-b border-inactive",
                        docs.length - 1 === idx && "border-b-0"
                      )}
                      onClick={() => setOpen(false)}
                    >
                      <p className="font-semibold">{city.name}</p>
                      <p className="text-sm font-medium">{city.heading}</p>
                    </Link>
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
            <Tooltip>
              <TooltipTrigger className="self-start text-xl font-semibold pl-4 my-3">
                <Link href={""}>Events</Link>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm font-medium text-primary">Coming Soon</p>
              </TooltipContent>
            </Tooltip>
            <Link href={`/contact`} className="text-xl font-semibold pl-4 my-3">
              Contact Us
            </Link>

            <div className="w-full px-4 flex flex-col justify-center gap-10 mt-8">
              <Tooltip>
                <TooltipTrigger>
                  <Link
                    href={""}
                    className="px-4 ml-4 lg:px-7 py-3.5 rounded-full bg-gradient-to-br from-muted-primary via-primary to-primary font-semibold text-white"
                  >
                    DOWNLOAD APP
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm font-medium text-primary">
                    Coming soon
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
          </SheetContent>
        </Sheet>
      </nav>
    </>
  );
}
