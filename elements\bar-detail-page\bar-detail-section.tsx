"use client";

import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "@/components/common/Accordion";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import RatingStars from "@/components/common/RatingStars";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import COLORS from "@/constants/colors";
import { BarsQuery } from "@/generated/graphql";
import { useHandleToggleAccordion } from "@/hooks/use-utils";
import { cn, DAYS, formatMilitaryTime } from "@/lib/utils";
import ICONS from "@/public/icons";
import { format } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import BarDetail from ".";

type BarDetailSectionProps = {
  bar: BarsQuery["bars"]["docs"][0];
  otherBars: BarsQuery["bars"];
};

const today = new Date();
const dayOfWeek = format(today, "EEEE").toUpperCase();

export default function BarDetailSection({
  bar,
  otherBars,
}: BarDetailSectionProps) {
  const barData = bar;
  const [selectedImage, setSelectedImage] = useState<number>(0);
  const { activeIndex, handleToggle } = useHandleToggleAccordion(0);
  const faqDetails = {
    barName: bar.name,
    address: bar.address?.address,
    happyHourEnd: bar.happyHours?.schedule?.[1]?.timings?.[1],
    happyHourStart: bar.happyHours?.schedule?.[0]?.timings?.[0],
    closestMetroStation: bar.address?.metroStation,
    happyHourPrice: bar.menu?.happyHourStartingPrice
      ? bar.menu?.happyHourStartingPrice
      : undefined,
    openingHour: bar.businessHours?.schedule?.[0]?.timings?.[0],
    closingHour: bar.businessHours?.schedule?.[1]?.timings?.[1],
    currency: bar.menu?.currency,
  };

  return (
    <section>
      <div className="w-full lg:py-[40px] sm:bg-gradient-to-b sm:from-footerbg sm:to-white">
        <div className="flex flex-col md:flex-row md:items-start md:justify-center px-4 container md:gap-[20px] xl:gap-[58px]">
          <div className="flex flex-col gap-3 xl:gap-3.5 mb-5 w-full overflow-x-auto hide-scrollbar">
            <Image
              src={barData.images[selectedImage] || "/svg/cheers.svg"}
              width={365}
              height={250}
              alt="club1"
              className="rounded-[18px] w-full h-[250px] lg:h-[400px] object-cover transition-all duration-300 ease-in-out aspect-square"
              quality={100}
              loading="eager"
              priority={true}
            />
            {barData.images.length > 1 ? (
              <div className="w-full">
                <HorizontalScrollCards>
                  {barData.images.map((image, idx) => (
                    <button
                      key={idx}
                      onClick={() => setSelectedImage(idx)}
                      className={cn(
                        "bg-transparent cursor-pointer overflow-hidden rounded-[18px] min-w-[85px] min-h-[92px] lg:min-w-[144px] lg:min-h-[156px] ",
                        idx === selectedImage &&
                          "ring-[3px] lg:ring-[6px] ring-primary"
                      )}
                    >
                      <Image
                        src={image || "/svg/tempLogo.svg"}
                        width={85}
                        height={92}
                        alt={`bar-image-${idx}`}
                        className="min-w-[85px] min-h-[92px] lg:min-w-[144px] lg:min-h-[156px] object-cover aspect-square"
                        quality={100}
                        loading="eager"
                        priority={true}
                      />
                    </button>
                  ))}
                </HorizontalScrollCards>
              </div>
            ) : (
              <div className="w-full h-[92px] lg:h-[156px]" />
            )}
          </div>
          <div className="w-full">
            <RatingStars
              className="bg-transparent border-0 lg:hidden"
              ratingValue={barData?.rating || 0}
              width={23}
              height={22}
              textStyles="text-text text-[24px] font-bold ml-2"
            />
            <RatingStars
              className="bg-transparent border-0 lg:mb-8 hidden lg:block"
              ratingValue={barData?.rating || 0}
              width={35}
              height={32}
              textStyles="text-text text-[24px] font-bold ml-2"
            />
            <div className="text-[32px] font-bold leading-9 text-text sm:text-3xl xl:text-6xl xl:leading-16">
              {barData?.name}
            </div>
            <div className="flex flex-row items-center w-full mt-2 gap-2 mb-2.5 lg:mt-[50px]  lg:mb-5">
              <div className="min-w-[38px] min-h-[38px] rounded-full bg-text flex items-center justify-center">
                <ICONS.PinIcon
                  width={20}
                  height={25}
                  color={COLORS.LIGHT.FOOTERBG}
                  className="lg:hidden"
                />
                <ICONS.PinIcon
                  width={28}
                  height={32}
                  color={COLORS.LIGHT.FOOTERBG}
                  className="hidden lg:flex"
                />
              </div>
              <div className="text-sm font-medium text-text lg:text-xl w-full">
                {barData?.address?.address}
              </div>
            </div>
            <div className="w-full flex flex-row items-center gap-1.5 flex-wrap mb-[28px]">
              {barData?.tags?.slice(0, 2).map((tag, idx) => (
                <button
                  key={idx}
                  className="text-text text-xl lg:text-2xl font-medium bg-muted-primary rounded-[4px] px-2 py-1 flex items-center justify-center"
                >
                  {tag?.name}
                </button>
              ))}
              {barData?.tags?.length > 2 && (
                <Tooltip>
                  <TooltipTrigger>
                    <p className="text-text text-xl lg:text-2xl font-medium bg-transparent ring-2 ring-muted-primary rounded-[4px] px-2 py-0.5 flex items-center justify-center">
                      +{barData.tags.length - 2}
                    </p>
                  </TooltipTrigger>
                  <TooltipContent className="flex flex-row items-center gap-1 py-2 bg-text">
                    {barData?.tags?.map((tag, idx) => (
                      <p
                        key={idx}
                        className="text-xs lg:text-sm font-medium bg-muted-primary rounded-[4px] px-2 py-1 flex items-center justify-center"
                      >
                        {tag?.name}
                      </p>
                    ))}
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
            <p className="text-sm sm:text-base lg:text-xl leading-6 text-text mt-[25px] mb-[15px] sm:my-7 sm:max-w-[550px]">
              {barData?.description}
            </p>
            <div className="flex flex-row items-center gap-2 text-xl lg:text-2xl text-secondary font-bold mb-[25px] lg:mb-[30px]">
              <div>Contact:</div>
              <Link
                className="hover:underline hover:underline-offset-2"
                href={`tel:${barData?.contact?.countryCode} ${barData?.contact?.phone}`}
              >
                {barData?.contact?.countryCode} {barData?.contact?.phone}
              </Link>
            </div>
            <Tooltip>
              <TooltipTrigger>
                <div className="px-10 btn-gradient-4 sm:px-[28px] py-[13px] mb-[33px] lg:text-xl font-bold font-montserrat leading-5 text-white rounded-full cursor-pointer ">
                  BOOK A TABLE
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm font-medium text-primary">Coming Soon</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
      </div>

      {/* DETAILS */}
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-16 container px-4 mb-[10px] lg:mb-[40px]">
        {/* OPENING HOURS */}
        {barData?.businessHours?.schedule &&
          barData.businessHours.schedule.length > 0 && (
            <div className="w-full flex flex-col gap-4 col-span-1">
              <h2 className="font-bold text-primary text-xl sm:text-2xl leading-5">
                Opening hours:
              </h2>
              <div className="bg-transparent sm:bg-backgroundlight py-1 sm:py-[28px] sm:px-[25px] sm:mt-[27px] mb-8 sm:mb-0">
                {DAYS.map((weekDay, idx) => (
                  <div
                    key={idx}
                    className={cn(
                      "flex flex-row items-center justify-between py-[7px] sm:py-[12px] border-b border-secondary",
                      idx === DAYS.length - 1 && "border-b-0"
                    )}
                  >
                    <div
                      className={cn(
                        "text-sm sm:text-xl leading-5 text-secondary lg:pb-2",
                        weekDay === dayOfWeek && "font-bold"
                      )}
                    >
                      {weekDay}
                    </div>
                    {(() => {
                      const daySchedule = barData?.businessHours?.schedule.find(
                        (day) => day.day === weekDay
                      );

                      if (!daySchedule || daySchedule.timings.length === 0) {
                        return (
                          <span
                            className={cn(
                              "text-sm sm:text-xl text-red-400",
                              weekDay === dayOfWeek && "font-bold"
                            )}
                          >
                            CLOSED
                          </span>
                        );
                      }

                      return (
                        <span
                          className={cn(
                            "text-sm sm:text-xl leading-5 text-secondary pb-2",
                            weekDay === dayOfWeek && "font-bold"
                          )}
                        >
                          {formatMilitaryTime(daySchedule.timings[0])} -{" "}
                          {formatMilitaryTime(daySchedule.timings[1])}
                        </span>
                      );
                    })()}
                  </div>
                ))}
              </div>
            </div>
          )}

        {/* HAPPY HOURS */}
        {barData?.happyHours?.schedule &&
          barData.happyHours.schedule.length > 0 && (
            <div className="w-full flex flex-col gap-4 col-span-1">
              <h2 className="font-bold text-primary text-xl sm:text-2xl leading-5">
                Happy hours:
              </h2>
              <div className="bg-transparent sm:bg-backgroundlight py-1 sm:py-[28px] sm:px-[25px] sm:mt-[27px] mb-8 sm:mb-0 h-full">
                {DAYS.map((weekDay, idx) => (
                  <div
                    key={idx}
                    className={cn(
                      "flex flex-row items-center justify-between py-[7px] sm:py-[12px] border-b border-secondary",
                      idx === DAYS.length - 1 && "border-b-0"
                    )}
                  >
                    <div
                      className={cn(
                        "text-sm sm:text-xl leading-5 text-secondary lg:pb-2 pb-1",
                        weekDay === dayOfWeek && "font-bold"
                      )}
                    >
                      {weekDay}
                    </div>
                    {(() => {
                      const daySchedule = barData?.happyHours?.schedule.find(
                        (day) => day.day === weekDay
                      );

                      if (
                        !daySchedule ||
                        daySchedule.timings.length === 0 ||
                        weekDay === DAYS[5] || // SATURDAY
                        weekDay === DAYS[6] // SUNDAY
                      ) {
                        return (
                          <span
                            className={cn(
                              "text-sm sm:text-xl text-red-400",
                              weekDay === dayOfWeek && "font-bold"
                            )}
                          >
                            N/A
                          </span>
                        );
                      }

                      return (
                        <span
                          className={cn(
                            "text-sm sm:text-xl leading-5 text-secondary",
                            weekDay === dayOfWeek && "font-bold"
                          )}
                        >
                          {formatMilitaryTime(daySchedule.timings[0])} -{" "}
                          {formatMilitaryTime(daySchedule.timings[1])}
                        </span>
                      );
                    })()}
                  </div>
                ))}
              </div>
            </div>
          )}
      </div>
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-16 container px-4 mb-[30px] ">
        {/* MENU */}
        {barData.menu && (
          <div
            className={cn(
              "w-full h-full flex flex-col gap-4",
              activeIndex === null && "h-full max-h-full"
            )}
          >
            <h2 className="font-bold text-primary text-xl sm:text-2xl leading-5">
              Menu
            </h2>
            <div className="w-full h-full overflow-y-auto hide-scrollbar flex flex-col gap-4 lg:py-3">
              {barData.menu?.sections.map((menu, idx) => (
                <Accordion
                  key={idx}
                  className="w-full flex flex-col gap-2 lg:gap-3"
                >
                  <div>
                    <AccordionTrigger
                      className={cn(
                        "justify-between bg-footerbg rounded-[8px] py-2 text-secondary text-base lg:text-xl",
                        activeIndex === idx && "rounded-b-0"
                      )}
                      onClick={handleToggle(idx)}
                      isActive={activeIndex === idx}
                    >
                      {menu.name}
                      {activeIndex === idx ? (
                        <ICONS.ChevronUpIcon
                          width={13}
                          height={7}
                          color={COLORS.LIGHT.SECONDARY}
                        />
                      ) : (
                        <ICONS.ChevronDownIcon
                          width={13}
                          height={7}
                          color={COLORS.LIGHT.SECONDARY}
                        />
                      )}
                    </AccordionTrigger>

                    <div
                      className={cn(
                        "max-h-[230px] overflow-y-auto scrollbar-theme relative"
                      )}
                    >
                      {activeIndex === idx && (
                        <div className="flex flex-row items-center justify-between w-full py-1.5 lg:py-2 px-2 bg-text sticky top-0 rounded-b-[8px] z-50">
                          <div className="flex flex-1">
                            <ICONS.BeerJugIcon
                              width={14}
                              height={14}
                              color={COLORS.LIGHT.MUTED_PRIMARY}
                              className="w-[14px] h-[14px] sm:w-[20px] sm:h-[21px]"
                            />
                          </div>
                          <div
                            className={cn(
                              "flex flex-1 flex-row items-center justify-center",
                              menu.items.filter(
                                (item) => item.happyHourPrice
                              ) && "justify-between gap-8"
                            )}
                          >
                            {menu.items.filter(
                              (item) => item.happyHourPrice
                            ) && (
                              <div className="text-[11px] sm:text-base xl:text-xl font-medium text-muted-primary truncate">
                                Happy Hour
                              </div>
                            )}
                            <div
                              className={cn(
                                "text-[11px] sm:text-base xl:text-xl font-medium text-muted-primary ml-3 lg:ml-3.5",
                                menu.items.filter((item) => item.happyHourPrice)
                                  .length < 0 &&
                                  "flex flex-1 items-center justify-center"
                              )}
                            >
                              Standard
                            </div>
                          </div>
                        </div>
                      )}
                      <AccordionItem isActive={activeIndex === idx}>
                        {menu.items.map((item, itemIdx) => (
                          <div
                            key={itemIdx}
                            className={cn(
                              "flex flex-row justify-between py-1.5 relative px-2",
                              menu.items.length - 1 === itemIdx
                                ? "border-b-0"
                                : "border-b border-secondary/30"
                            )}
                          >
                            <div className="text-xs sm:text-sm lg:text-xl font-bold text-secondary flex flex-1 ">
                              {item.name}
                            </div>
                            <div
                              className={cn(
                                "flex flex-1 flex-row items-center justify-center  gap-7 sm:gap-[40px] sm:px-4",
                                menu.items.filter(
                                  (item) => item.happyHourPrice
                                ) && "justify-between"
                              )}
                            >
                              {item.happyHourPrice !== undefined && (
                                <div
                                  className={cn(
                                    "text-xs sm:text-base xl:text-xl font-medium text-secondary truncate ml-4 sm:ml-8"
                                  )}
                                >
                                  {item.happyHourPrice === 0 ||
                                  item.happyHourPrice === null
                                    ? "-"
                                    : `€ ${item.happyHourPrice}`}
                                </div>
                              )}

                              <div className="text-xs sm:text-base xl:text-xl font-medium text-secondary truncate mr-3 sm:mr-2">
                                € {item.price}
                              </div>
                            </div>
                          </div>
                        ))}
                      </AccordionItem>
                    </div>
                  </div>
                </Accordion>
              ))}
            </div>
          </div>
        )}
      </div>
      <BarDetail.CallToDownloadSection />
      <BarDetail.ExploreOtherBarsSection bars={otherBars.docs} />
      <BarDetail.FAQSection {...faqDetails} />
    </section>
  );
}
