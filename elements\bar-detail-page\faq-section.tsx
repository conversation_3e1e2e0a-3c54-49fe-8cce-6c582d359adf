"use client";

import AcordionIcons from "@/components/common/AccordionIcons";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { formatMilitaryTime } from "@/lib/utils";
import { useEffect, useRef, useState } from "react";

type FAQProps = {
  barName: string | undefined;
  address: string | undefined;
  happyHourEnd: number | undefined;
  happyHourStart: number | undefined;
  closestMetroStation: string | undefined | null;
  happyHourPrice: number | undefined | null;
  openingHour: number | undefined;
  closingHour: number | undefined;
  currency: string | undefined;
};

export default function FAQSection({
  barName,
  address,
  happyHourEnd,
  happyHourStart,
  closestMetroStation,
  happyHourPrice,
  closingHour,
  openingHour,
  currency,
}: FAQProps) {
  const lastFAQRef = useRef<HTMLDivElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const [openItem, setOpenItem] = useState<string | null>("FAQ-1");

  useEffect(() => {
    if (openItem === "FAQ-last" && lastFAQRef.current && containerRef.current) {
      const container = containerRef.current;
      const item = lastFAQRef.current;

      // Scroll only the FAQ section
      container.scrollTo({
        top: item.offsetTop - container.offsetTop + 40,
        behavior: "smooth",
      });
    }
  }, [openItem]);

  return (
    <section className="w-full h-[630px] flex flex-col items-center container pb-[50px]">
      <h2 className="text-xl lg:text-3xl xl:text-[40px] font-bold text-text mt-3 xl:mt-4 text-center">
        Frequently Asked Questions
      </h2>
      <div
        ref={containerRef}
        className="mt-[42px] overflow-y-auto scrollbar-theme w-full max-w-[1079px] flex flex-col gap-2"
      >
        <Accordion
          type="single"
          collapsible
          onValueChange={(val) => setOpenItem(val)}
        >
          <AccordionItem value="FAQ-1">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6 text-text"
              chevron={false}
            >
              <AcordionIcons />
              <div>
                Where is{" "}
                <span className="text-primary font-semibold">{barName}</span>{" "}
                Located?
              </div>
            </AccordionTrigger>
            <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
              <span className="font-medium text-secondary">{barName}</span> is
              located at{" "}
              <span className="font-bold text-secondary">{address}.</span>
            </AccordionContent>
          </AccordionItem>
          {happyHourStart && happyHourEnd && (
            <AccordionItem value="FAQ-2">
              <AccordionTrigger
                className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
                chevron={false}
              >
                <AcordionIcons />
                <div>
                  Do <span className="font-bold text-primary">{barName}</span>{" "}
                  have happy hours?
                </div>
              </AccordionTrigger>
              <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
                Yes,{" "}
                <span className="font-medium text-secondary">{barName}</span>{" "}
                offers happy hours from
                <span className="font-bold text-secondary mx-1.5">
                  {happyHourStart && formatMilitaryTime(happyHourStart)}
                </span>
                to
                <span className="font-bold text-secondary mx-1.5">
                  {happyHourEnd && formatMilitaryTime(happyHourEnd)}.
                </span>
              </AccordionContent>
            </AccordionItem>
          )}

          <AccordionItem value="FAQ-3">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
              chevron={false}
            >
              <AcordionIcons />
              <div>
                What&apos;s the closest Metro station to
                <span className="font-bold ml-1.5 text-primary">{barName}</span>
                ?{" "}
              </div>
            </AccordionTrigger>
            <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
              The closest metro station to{" "}
              <span className="font-medium text-secondary"> {barName} </span> is{" "}
              <span className="font-bold text-secondary">
                {closestMetroStation}.
              </span>
            </AccordionContent>
          </AccordionItem>

          {happyHourPrice && (
            <AccordionItem value="FAQ-4">
              <AccordionTrigger
                className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
                chevron={false}
              >
                <AcordionIcons />
                <div>
                  What is the starting price for drinks at
                  <span className="font-bold ml-1.5 text-primary">
                    {barName}
                  </span>
                  ?
                </div>
              </AccordionTrigger>
              <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
                Drinks at{" "}
                <span className="font-medium text-secondary">{barName}</span>{" "}
                start at{" "}
                <span className="font-bold text-secondary">
                  € {happyHourPrice}
                </span>{" "}
                during happy hour.
              </AccordionContent>
            </AccordionItem>
          )}

          <AccordionItem value="FAQ-5">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
              chevron={false}
            >
              <AcordionIcons />
              <div>
                What kind of drinks are available at
                <span className="font-bold ml-1.5 text-primary">{barName}</span>
                ?
              </div>
            </AccordionTrigger>
            <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
              <span className="font-bold text-secondary">
                Draft Beers, Bottled Beers, Cocktails
              </span>{" "}
              are available at{" "}
              <span className="font-medium text-secondary">{barName}.</span>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="FAQ-last">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
              chevron={false}
            >
              <AcordionIcons />
              <div>
                What are the opening hours for
                <span className="font-bold ml-1.5 text-primary">{barName}</span>
                ?
              </div>
            </AccordionTrigger>
            <AccordionContent
              ref={lastFAQRef}
              className="text-sm lg:text-base px-6 lg:px-[59px] py-5"
            >
              <span>
                Opening hours for
                <span className="font-medium text-secondary ml-1.5">
                  {barName}
                </span>{" "}
                is
                <span className="font-bold text-secondary mx-1.5">
                  {formatMilitaryTime(openingHour as number)}
                </span>
                <span>to</span>
                <span className="font-bold text-secondary mx-1.5">
                  {formatMilitaryTime(closingHour as number)},{" "}
                </span>
              </span>
              Opening hours may vary on holidays or during special events, check
              their social media for updates.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </section>
  );
}
