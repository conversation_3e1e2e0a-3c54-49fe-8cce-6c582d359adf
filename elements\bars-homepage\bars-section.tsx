import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import BarCard from "@/components/cards/bar.card";
import GridCard from "@/components/cards/grid.card";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import {
  BarCategoriesDocument,
  BarCategoriesQuery,
  BarCategoriesQueryVariables,
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
} from "@/generated/graphql";
import Link from "next/link";

export const revalidate = 3600;

type BarsProps = {
  cityId: string;
};

export default async function BarsSection({ cityId }: BarsProps) {
  try {
    const { bars: popularBars } = await fetchData<
      BarsQuery,
      BarsQueryVariables
    >(BarsDocument, {
      barsInput: { city: cityId, featured: true },
      paginationInput: { limit: 6 },
    })();

    const {
      barCategories: { docs: barCategories },
    } = await fetchData<BarCategoriesQuery, BarCategoriesQueryVariables>(
      BarCategoriesDocument,
      { paginationInput: { limit: 5 } }
    )();

    const categorySections = await Promise.all(
      barCategories.map(async (c) => {
        const { bars: barsWithFilter } = await fetchData<
          BarsQuery,
          BarsQueryVariables
        >(BarsDocument, {
          barsInput: {
            city: cityId,
            categories: [c.id],
          },
          paginationInput: { limit: 8 },
        })();

        if (!barsWithFilter.docs.length) return null;

        return (
          <section
            className="w-full flex flex-col items-center container mb-[45px]"
            key={c.id}
          >
            <h2 className="text-2xl lg:text-[40px] font-bold text-text text-center mb-4 lg:mb-6">
              {c.name}
            </h2>
            <div className="w-full flex flex-col items-center justify-center">
              <HorizontalScrollCards>
                {barsWithFilter?.docs?.map((card) => (
                  <Link href={`bars/${card.slug}`} key={card.id}>
                    <BarCard
                      data={card}
                      cardType="bar"
                      wrapperClassName="w-[298px]"
                    />
                  </Link>
                ))}
              </HorizontalScrollCards>
              <Link
                className="px-7 py-[11px] font-semibold my-[20px] lg:my-6 bg-primary text-white rounded-full"
                href={{ query: { category: c.name, page: 1 } }}
              >
                View all
              </Link>
            </div>
          </section>
        );
      })
    );

    return (
      <div className="bars-section-background-gradient w-full pt-[45px] xl:pt-[98px]">
        {popularBars?.docs.length > 0 && (
          <section className="w-full flex flex-col container mb-[45px] xl:mb-[88px]">
            <h2 className="text-2xl lg:text-[40px] font-bold text-text text-center mb-4 lg:mb-6">
              Popular Bars
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-7 sm:gap-2 xl:gap-[38px]">
              {popularBars?.docs.map((bar) => (
                <Link href={`bars/${bar.slug}`} key={bar.id}>
                  <GridCard
                    name={bar.name}
                    rating={bar.rating}
                    avgCost={bar.menu?.startingPrice ?? 0}
                    image={bar.coverImage!}
                  />
                </Link>
              ))}
            </div>
          </section>
        )}
        {categorySections}
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
