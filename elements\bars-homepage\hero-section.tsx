export const revalidate = 3600;

export default function HeroSection({
  category,
  description,
}: {
  category: string | undefined;
  description: string | undefined | null;
}) {
  return (
    <section className="w-full py-[112px] md:py-[128px] bars-banner-gradient flex flex-col items-center justify-center">
      <div className="container w-full flex flex-col items-center justify-center">
        {category ? (
          <div className="font-bold text-text text-[32px] leading-10.5 text-center sm:text-[40px] sm:leading-[52px]">
            {category}
          </div>
        ) : (
          <div className="font-bold text-text text-[32px] leading-10.5 text-center sm:text-[40px] sm:leading-[52px]">
            Bars
          </div>
        )}
        {category ? (
          <p className="text-sm text-text sm:text-xl mt-[40px] text-center">
            {description}
          </p>
        ) : (
          <p className="text-sm text-text sm:text-xl mt-[40px] text-center">
            Discover the best bars to experience going out like a local without
            breaking the bank. Whether you&apos;re looking for a buzzing student
            bar with cheap pints and board games, a stylish cocktail bar with
            expert mixologists, or a hidden speakeasy for an intimate date
            night? We got you covered. Paris, Marseille, Montpellier, Nice, Lyon
            and other cities in France offer happy hour deals in (some
            throughout the night) where you never have to pay full price for the
            drinks. Embrace the vibrant and unique bar scene, and make the most
            of happy hours.
          </p>
        )}
      </div>
    </section>
  );
}
