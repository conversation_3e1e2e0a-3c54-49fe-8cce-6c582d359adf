"use client";

import AcordionIcons from "@/components/common/AccordionIcons";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { formatMilitaryTime } from "@/lib/utils";
import { useRef, useEffect, useState } from "react";

export const revalidate = 3600;

type FAQProps = {
  clubName: string | undefined;
  address: string | undefined;
  closestMetroStation: string | undefined | null;
  openingHour: number | undefined;
  closingHour: number | undefined;
  currency: string | undefined;
};

export default function FAQSection({
  clubName,
  address,
  closestMetroStation,
  closingHour,
  openingHour,
}: FAQProps) {
  const lastFAQRef = useRef<HTMLDivElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const [openItem, setOpenItem] = useState<string | null>("FAQ-1");

  useEffect(() => {
    if (openItem === "FAQ-last" && lastFAQRef.current && containerRef.current) {
      const container = containerRef.current;
      const item = lastFAQRef.current;

      // Scroll only the FAQ section
      container.scrollTo({
        top: item.offsetTop - container.offsetTop + 40,
        behavior: "smooth",
      });
    }
  }, [openItem]);

  return (
    <section className="w-full h-[600px] flex flex-col items-center container pb-[120px] xl:pb-[88px]">
      <h2 className="text-xl lg:text-3xl xl:text-[40px] font-bold text-text mt-3 xl:mt-4 text-center">
        Frequently Asked Questions
      </h2>
      <div
        ref={containerRef}
        className="mt-[42px] overflow-y-auto scrollbar-theme w-full max-w-[1079px] flex flex-col gap-2 "
      >
        <Accordion
          type="single"
          collapsible
          onValueChange={(val) => setOpenItem(val)}
        >
          <AccordionItem value="FAQ-1">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6 text-text"
              chevron={false}
            >
              <AcordionIcons />
              <div>
                Where is
                <span className="font-bold mx-1.5 text-primary">
                  {clubName}
                </span>
                Located?
              </div>
            </AccordionTrigger>
            <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
              <span className="font-medium text-secondary">{clubName}</span> is
              located at{" "}
              <span className="font-bold text-secondary">{address}</span>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="FAQ-2">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
              chevron={false}
            >
              <AcordionIcons />
              <div>
                What&apos;s the closest Metro station to
                <span className="font-bold ml-1.5 text-primary">
                  {clubName}
                </span>
                ?
              </div>
            </AccordionTrigger>
            <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
              The closest metro station to{" "}
              <span className="font-medium text-secondary"> {clubName} </span>{" "}
              is{" "}
              <span className="font-bold text-secondary">
                {closestMetroStation}
              </span>
            </AccordionContent>
          </AccordionItem>

          {/* <AccordionItem value="FAQ-3">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
              chevron={false}
            >
              <AcordionIcons />
              What kind of music does the
              <span className="font-bold -mx-2 text-primary">{clubName}</span>
              play?
            </AccordionTrigger>
            <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
              The <span className="font-medium text-secondary">{clubName}</span>{" "}
              plays
              <span className="font-bold text-secondary"> "Music Type" </span>.
            </AccordionContent>
          </AccordionItem> */}

          <AccordionItem value="FAQ-3">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
              chevron={false}
            >
              <AcordionIcons />
              <div>
                What are the opening hours for
                <span className="font-bold ml-1.5 text-primary">
                  {clubName}
                </span>
                ?
              </div>
            </AccordionTrigger>
            <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
              <span className="font-medium text-secondary">{clubName}</span> is
              open from
              <span className="font-bold text-secondary mx-1.5">
                {formatMilitaryTime(openingHour as number)}
              </span>
              <span>to</span>
              <span className="font-bold text-secondary ml-1.5">
                {formatMilitaryTime(closingHour as number)}
              </span>
              . Opening hours may vary on holidays or during special events,
              check their social media for updates.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="FAQ-4">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
              chevron={false}
            >
              <AcordionIcons />
              <div>
                What is the minimum age to enter the
                <span className="font-bold ml-1.5 text-primary">
                  {clubName}
                </span>
                ?
              </div>
            </AccordionTrigger>
            <AccordionContent className="text-sm lg:text-base px-6 lg:px-[59px] py-5">
              The minimum age is usually
              <span className="font-bold text-secondary mx-1.5">18</span>or
              <span className="font-bold text-secondary mx-1.5">21</span>
              depending on local laws. A
              <span className="font-bold text-secondary mx-1.5">valid ID</span>
              or
              <span className="font-bold text-secondary mx-1.5">passport</span>
              is required for entry.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="FAQ-last">
            <AccordionTrigger
              className="text-sm lg:text-base bg-[#FFF9EC] px-4 py-6"
              chevron={false}
            >
              <AcordionIcons />
              Do I need to book a table in advance?
            </AccordionTrigger>
            <AccordionContent
              ref={lastFAQRef}
              className="text-sm lg:text-base px-6 lg:px-[59px] py-5"
            >
              It is recommended to book a table or VIP area in advance,
              especially on weekends or for special events, to guarantee entry
              and seating.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </section>
  );
}
