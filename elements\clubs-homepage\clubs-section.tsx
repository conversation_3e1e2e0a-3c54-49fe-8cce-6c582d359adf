import NotFound from "@/app/not-found";
import { fetchData } from "@/client";
import BarCard from "@/components/cards/bar.card";
import GridCard from "@/components/cards/grid.card";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import {
  ClubCategoriesDocument,
  ClubCategoriesQuery,
  ClubCategoriesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import Link from "next/link";

export const revalidate = 3600;

type ClubsProps = {
  cityId: string;
};

export default async function ClubsSection({ cityId }: ClubsProps) {
  try {
    const { clubs: popularClubs } = await fetchData<
      ClubsQuery,
      ClubsQueryVariables
    >(ClubsDocument, {
      clubsInput: { city: cityId, featured: true },
      paginationInput: { limit: 6 },
    })();

    const {
      clubCategories: { docs: clubCategories },
    } = await fetchData<ClubCategoriesQuery, ClubCategoriesQueryVariables>(
      ClubCategoriesDocument,
      { paginationInput: { limit: 5 } }
    )();

    const categorySections = await Promise.all(
      clubCategories.map(async (c) => {
        const { clubs: clubsWithFilter } = await fetchData<
          ClubsQuery,
          ClubsQueryVariables
        >(ClubsDocument, {
          clubsInput: {
            city: cityId,
            categories: [c.id],
          },
          paginationInput: { limit: 10 },
        })();

        if (!clubsWithFilter.docs.length) return null;

        return (
          <section
            className="w-full flex flex-col items-center container mb-[45px]"
            key={c.id}
          >
            <h2 className="text-2xl lg:text-[40px] font-bold text-text text-center mb-4 lg:mb-6">
              {c.name}
            </h2>
            <div className="w-full flex flex-col items-center justify-center">
              <HorizontalScrollCards>
                {clubsWithFilter?.docs?.map((card) => (
                  <Link href={`clubs/${card.slug}`} key={card.id}>
                    <BarCard
                      data={card}
                      cardType="club"
                      wrapperClassName="w-[298px]"
                    />
                  </Link>
                ))}
              </HorizontalScrollCards>
              <Link
                className="px-7 py-[11px] font-semibold my-[20px] lg:my-6 bg-primary text-white rounded-full"
                href={{ query: { category: c.name, page: 1 } }}
              >
                View all
              </Link>
            </div>
          </section>
        );
      })
    );

    return (
      <div className="bars-section-background-gradient w-full pt-[45px] xl:pt-[98px]">
        {popularClubs?.docs.length > 0 && (
          <section className="w-full flex flex-col container mb-[45px] xl:mb-[88px]">
            <h2 className="text-2xl lg:text-[40px] font-bold text-text  text-center mb-4 lg:mb-6">
              Popular Clubs
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-7 sm:gap-2 xl:gap-[38px]">
              {popularClubs?.docs?.map((club) => (
                <Link href={`clubs/${club.slug}`} key={club.id}>
                  <GridCard
                    name={club.name}
                    rating={club.rating}
                    avgCost={club.menu?.startingPrice ?? 0}
                    image={club.coverImage!}
                  />
                </Link>
              ))}
            </div>
          </section>
        )}
        {categorySections}
      </div>
    );
  } catch (error) {
    return <NotFound error={error as Error} />;
  }
}
