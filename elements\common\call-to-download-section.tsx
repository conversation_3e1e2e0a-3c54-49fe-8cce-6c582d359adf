import Image from "next/image";
import React from "react";
import {
  Toolt<PERSON>,
  Too<PERSON><PERSON>Content,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function CallToDownloadSection() {
  return (
    <>
      {/* MOBILE VIEW */}
      <section className="w-full h-[870px] flex justify-center relative overflow-hidden sm:hidden">
        <div className="w-full h-[636px] relative">
          <Image
            src={"/images/calltodownloadBG_mobile.webp"}
            fill
            className="object-cover absolute"
            quality={100}
            loading="eager"
            sizes="(max-width: 768px) 100vw"
            alt="download-app"
          />
          <div className="absolute z-10 px-4">
            <h2 className="text-[32px] h-[120px] font-montserrat-alternates font-medium leading-12 text-white max-w-[365px] mt-14.5 mb-12.5">
              Simple way to explore nightlife destinations
            </h2>
            <div className="flex flex-col gap-1">
              <Tooltip>
                <TooltipTrigger>
                  <div className="w-[160px] bg-white flex items-center justify-center rounded-xl py-3 mb-2 ">
                    <Image
                      src={"/svg/googleplay-black.svg"}
                      alt="googleplay-button"
                      width={132}
                      height={38}
                      quality={100}
                      loading="eager"
                      className="object-contain w-[132px] h-[38px]"
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-primary text-sm font-medium">
                    Coming Soon
                  </p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger>
                  <div className="w-[160px] bg-white flex items-center justify-center rounded-xl py-3 mb-2">
                    <Image
                      src={"/svg/applestore-black.svg"}
                      alt="applestore-button"
                      width={132}
                      height={38}
                      quality={100}
                      loading="eager"
                      className="object-contain w-[132px] h-[38px]"
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-primary text-sm font-medium">
                    Coming Soon
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className="w-full flex flex-row items-center justify-center gap-1">
              <div className="w-[170px] h-[360px] overflow-hidden  mt-4 relative">
                <Image
                  src={"/images/home.webp"}
                  alt="splash"
                  fill
                  quality={100}
                  loading="eager"
                  sizes="(max-width: 768px) 100vw"
                  className="object-cover"
                />
              </div>
              <div className="w-[170px] h-[360px] relative overflow-hidden mt-32">
                <Image
                  src={"/images/bar.webp"}
                  alt="splash"
                  fill
                  quality={100}
                  loading="eager"
                  sizes="(max-width: 768px) 100vw"
                  className="object-cover"
                />
              </div>
            </div>
          </div>
          <Image
            src={"/svg/pointing-arrow.svg"}
            width={120}
            height={90}
            className="absolute top-75 rotate-75 right-10 w-[120px] h-[90px]"
            alt="pointing-arrow"
            quality={100}
            loading="eager"
          />
        </div>
      </section>

      {/* TAB VIEW */}
      <section className="hidden sm:flex w-full h-[756px] relative mb-10 overflow-hidden sm:bg-transparent lg:hidden">
        <div className="w-full h-[595px] relative">
          <Image
            src={"/images/calltodownloadBG.webp"}
            fill
            alt="download-app"
            quality={100}
            loading="eager"
            className="object-cover"
            sizes="(max-width: 768px) 100vw"
          />
          <div className="w-full container mx-auto">
            <div className="relative">
              <h2 className="sm:text-[40px] h-[110px] font-montserrat-alternates font-medium leading-[50px] text-white max-w-[60%] mt-8 mb-16">
                Simple way to explore nightlife destinations.
              </h2>
              <div className="flex flex-row items-center gap-6">
                <Tooltip>
                  <TooltipTrigger>
                    <div className="w-[160px] bg-white flex items-center justify-center rounded-xl py-2">
                      <Image
                        src={"/svg/googleplay-black.svg"}
                        alt="googleplay-button"
                        width={132}
                        height={38}
                        quality={100}
                        loading="eager"
                        className="object-contain w-[132px] h-[38px]"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-primary text-sm font-medium">
                      Coming Soon
                    </p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger>
                    <div className="w-[160px] bg-white flex items-center justify-center rounded-xl py-2">
                      <Image
                        src={"/svg/applestore-black.svg"}
                        alt="applestore-button"
                        width={132}
                        height={38}
                        quality={100}
                        loading="eager"
                        className="object-contain w-[132px] h-[38px]"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-primary text-sm font-medium">
                      Coming Soon
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Image
                src={"/svg/pointing-arrow.svg"}
                width={180}
                height={90}
                className="-scale-y-90 w-auto h-auto rotate-10 mt-9 ml-[160px]"
                alt="pointing-arrow"
                quality={100}
                loading="eager"
              />
              <div className="w-full flex flex-row items-center justify-center gap-1 absolute top-50 left-45 scale-110">
                <div className="w-[170px] h-[360px] relative overflow-hidden mt-4">
                  <Image
                    src={"/images/home.webp"}
                    alt="splash"
                    fill
                    quality={100}
                    loading="eager"
                    sizes="(max-width: 768px) 100vw"
                    className="object-cover"
                  />
                </div>
                <div className="w-[170px] h-[360px] relative overflow-hidden mt-32">
                  <Image
                    src={"/images/bar.webp"}
                    alt="splash"
                    fill
                    quality={100}
                    loading="eager"
                    sizes="(max-width: 768px) 100vw"
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* WEB VIEW */}
      <section className=" hidden lg:flex lg:items-center lg:justify-center w-full h-[756px] relative mb-24 overflow-hidden">
        <div className="w-full h-[595px] relative flex flex-row items-center justify-center">
          <Image
            src={"/images/calltodownloadBG.webp"}
            fill
            className="object-cover"
            alt="download-app"
            quality={100}
            loading="eager"
            sizes="(min-width: 1024) 100vw"
          />
          <div className="w-full h-full container flex flex-row items-center justify-between absolute z-20">
            <div className="w-full">
              <h2 className="text-[48px] h-[160px] font-medium leading-[61px] text-white max-w-[409px] mb-[42px]">
                Simple way to explore nightlife destinations.
              </h2>
              <div className="flex flex-row items-center gap-6">
                <Tooltip>
                  <TooltipTrigger>
                    <div className="w-[213px] bg-white flex items-center justify-center rounded-xl py-3">
                      <Image
                        src={"/svg/googleplay-black.svg"}
                        alt="googleplay-button"
                        width={185}
                        height={50}
                        className="w-[150px] h-auto xl:w-[170px] xl:h-auto object-contain"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-primary text-sm font-medium">
                      Coming Soon
                    </p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger>
                    <div className="w-[213px] bg-white flex items-center justify-center rounded-xl py-3">
                      <Image
                        src={"/svg/applestore-black.svg"}
                        alt="applestore-button"
                        width={185}
                        height={50}
                        className="w-[150px] h-auto xl:w-[170px] xl:h-auto object-contain"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-primary text-sm font-medium">
                      Coming Soon
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
            <div className="w-full flex flex-row items-start gap-4 relative">
              <div className="w-[130px] h-[90px] absolute -left-40 top-90 rotate-10 xl:w-[180px] xl:h-[90px] xl:-left-60 xl:top-80 xl:rotate-20">
                <Image
                  src="/svg/pointing-arrow.svg"
                  alt="pointing-arrow"
                  fill
                  className="object-cover"
                  sizes="(min-width: 1024) 130px,(min-width: 1440) 180px"
                />
              </div>
              <div className="w-[240px] xl:w-[287px] h-[530px] xl:h-[624px] relative overflow-hidden  mb-[200px] xl:mb-[120px]">
                <Image
                  src={"/images/home.webp"}
                  alt="splash"
                  fill
                  quality={100}
                  loading="eager"
                  sizes="(max-width: 768px) 100vw"
                  className="object-cover"
                />
              </div>
              <div className="w-[240px] xl:w-[287px] h-[530px] xl:h-[624px] relative overflow-hidden  mt-[200px] xl:mt-[120px]">
                <Image
                  src={"/images/bar.webp"}
                  alt="splash"
                  fill
                  quality={100}
                  loading="eager"
                  sizes="(max-width: 768px) 100vw"
                  className="bg-cover aspect-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
