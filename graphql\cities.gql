query Cities($citiesInput: CitiesInput, $paginationInput: PaginationInput) {
  cities(citiesInput: $citiesInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      image
      coverImage
      heading
      subHeading
    }
  }
}

query CityStats($cityId: String!) {
  bars(barsInput: { city: $cityId }) {
    totalDocs
  }
  clubs(clubsInput: { city: $cityId }) {
    totalDocs
  }
}
