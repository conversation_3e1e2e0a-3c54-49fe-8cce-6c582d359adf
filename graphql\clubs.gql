query Clubs($clubsInput: ClubsInput, $paginationInput: PaginationInput) {
  clubs(clubsInput: $clubsInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      slug
      name
      description
      status
      logo
      coverImage
      images
      featured
      rating
      address {
        address
        metroLine
        metroStation
        location {
          center
        }
      }
      categories {
        name
        id
      }
      city {
        id
        name
      }
      tags {
        id
        name
      }
      businessHours {
        schedule {
          day
          timings
        }
      }
      contact {
        countryCode
        phone
      }
      menu {
        currency
        startingPrice
        sections {
          name
          items {
            name
            price
            available
          }
        }
      }
    }
  }
}

query ClubCategories($paginationInput: PaginationInput) {
  clubCategories(paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      description
      image
      icon
    }
  }
}

query ClubCategoryByName($name: String) {
  clubCategory(id: null, name: $name) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
  }
}
