import type { NextConfig } from "next";

//REMEMBER TO ADD SPECIFIC DOMAINS HERE

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**", // Accepts all hostnames (not recommended for production)
      },
    ],
  },
  async redirects() {
    return [
      {
        source: "/:lang(en|fr)/:path*",
        destination: "/:path*",
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
